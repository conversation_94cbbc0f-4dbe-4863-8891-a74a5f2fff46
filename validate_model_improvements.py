#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型改进价值验证脚本
快速验证GAT混合图架构是否有价值，避免浪费时间在无效改进上
"""

import os
import warnings
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from sklearn.model_selection import train_test_split
from tqdm import tqdm
import time
import argparse
from model import MVGNN
from utils import Seed_everything, Metric, TaskDataset
from loss import FocalLoss, CombinedLoss
from noam_opt import get_std_opt

warnings.simplefilter('ignore')

def quick_train_test(model, train_loader, val_loader, criterion, optimizer, device, max_epochs=3):
    """
    快速训练测试，只训练几个epoch来验证模型学习能力
    """
    model.train()
    
    results = {
        'epochs': [],
        'train_loss': [],
        'train_auc': [],
        'val_loss': [],
        'val_auc': [],
        'grad_norm': [],
        'time_per_epoch': []
    }
    
    print(f"🚀 开始快速验证训练（{max_epochs} epochs）...")
    
    for epoch in range(max_epochs):
        start_time = time.time()
        
        # Training phase
        model.train()
        train_loss = 0
        train_preds = []
        train_labels = []
        total_grad_norm = 0
        num_batches = 0
        
        for data in tqdm(train_loader, desc=f"Epoch {epoch+1}/{max_epochs} - Training"):
            _, protein_X, protein_node_features, protein_masks, y, adj, pe = data

            # Move data to device
            protein_X = protein_X.to(device)
            protein_node_features = protein_node_features.to(device)
            protein_masks = protein_masks.to(device)
            y = y.to(device)
            adj = adj.to(device)
            pe = pe.to(device)

            # Forward pass
            optimizer.zero_grad()
            outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)

            # Calculate loss
            loss = 0
            for i in range(len(protein_masks)):
                mask = protein_masks[i].bool()
                if mask.sum() > 0:
                    loss += criterion(outputs[i][mask], y[i][mask])

            loss = loss / len(protein_masks)
            
            # Backward pass
            loss.backward()
            
            # 梯度裁剪和监控
            grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            total_grad_norm += grad_norm.item()
            num_batches += 1
            
            optimizer.step()
            train_loss += loss.item()

            # Collect predictions
            with torch.no_grad():
                probs = torch.sigmoid(outputs)
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        train_preds.append(probs[i][mask].cpu().numpy())
                        train_labels.append(y[i][mask].cpu().numpy())

        # Calculate training metrics
        train_preds = np.concatenate([p.flatten() for p in train_preds])
        train_labels = np.concatenate([l.flatten() for l in train_labels])
        train_metrics = Metric(train_preds, train_labels)
        avg_grad_norm = total_grad_norm / num_batches if num_batches > 0 else 0

        # Validation phase
        model.eval()
        val_loss = 0
        val_preds = []
        val_labels = []
        
        with torch.no_grad():
            for data in tqdm(val_loader, desc=f"Epoch {epoch+1}/{max_epochs} - Validation"):
                _, protein_X, protein_node_features, protein_masks, y, adj, pe = data

                protein_X = protein_X.to(device)
                protein_node_features = protein_node_features.to(device)
                protein_masks = protein_masks.to(device)
                y = y.to(device)
                adj = adj.to(device)
                pe = pe.to(device)

                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
                
                loss = 0
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        loss += criterion(outputs[i][mask], y[i][mask])
                
                loss = loss / len(protein_masks)
                val_loss += loss.item()
                
                probs = torch.sigmoid(outputs)
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        val_preds.append(probs[i][mask].cpu().numpy())
                        val_labels.append(y[i][mask].cpu().numpy())

        # Calculate validation metrics
        val_preds = np.concatenate([p.flatten() for p in val_preds])
        val_labels = np.concatenate([l.flatten() for l in val_labels])
        val_metrics = Metric(val_preds, val_labels)
        
        epoch_time = time.time() - start_time
        
        # Store results
        results['epochs'].append(epoch + 1)
        results['train_loss'].append(train_loss / len(train_loader))
        results['train_auc'].append(train_metrics[0])
        results['val_loss'].append(val_loss / len(val_loader))
        results['val_auc'].append(val_metrics[0])
        results['grad_norm'].append(avg_grad_norm)
        results['time_per_epoch'].append(epoch_time)
        
        # Print progress
        print(f"Epoch {epoch+1}/{max_epochs}:")
        print(f"  Train Loss: {train_loss/len(train_loader):.6f}, Train AUC: {train_metrics[0]:.6f}")
        print(f"  Val Loss: {val_loss/len(val_loader):.6f}, Val AUC: {val_metrics[0]:.6f}")
        print(f"  Grad Norm: {avg_grad_norm:.6f}, Time: {epoch_time:.1f}s")
        print()

    return results

def analyze_results(results):
    """
    分析快速训练结果，判断模型是否有价值
    """
    print("📊 模型价值分析报告")
    print("=" * 50)
    
    # 基础指标
    final_train_auc = results['train_auc'][-1]
    final_val_auc = results['val_auc'][-1]
    initial_train_auc = results['train_auc'][0]
    initial_val_auc = results['val_auc'][0]
    
    # 学习能力分析
    train_auc_improvement = final_train_auc - initial_train_auc
    val_auc_improvement = final_val_auc - initial_val_auc
    
    # 稳定性分析
    avg_grad_norm = np.mean(results['grad_norm'])
    grad_norm_std = np.std(results['grad_norm'])
    
    # 效率分析
    avg_time_per_epoch = np.mean(results['time_per_epoch'])
    
    print(f"🎯 学习能力评估:")
    print(f"  初始 Train AUC: {initial_train_auc:.6f}")
    print(f"  最终 Train AUC: {final_train_auc:.6f} (提升: {train_auc_improvement:+.6f})")
    print(f"  初始 Val AUC: {initial_val_auc:.6f}")
    print(f"  最终 Val AUC: {final_val_auc:.6f} (提升: {val_auc_improvement:+.6f})")
    print()
    
    print(f"🔧 训练稳定性:")
    print(f"  平均梯度范数: {avg_grad_norm:.6f} ± {grad_norm_std:.6f}")
    print(f"  每epoch时间: {avg_time_per_epoch:.1f}s")
    print()
    
    # 价值判断
    print(f"💡 价值评估:")
    
    # 判断标准
    is_learning = final_train_auc > 0.55  # 明显超过随机猜测
    is_improving = train_auc_improvement > 0.01  # 有明显改进
    is_stable = avg_grad_norm < 10.0  # 梯度稳定
    is_generalizing = final_val_auc > 0.52  # 验证集有效果
    
    score = 0
    if is_learning:
        print(f"  ✅ 学习能力: PASS (Train AUC {final_train_auc:.3f} > 0.55)")
        score += 25
    else:
        print(f"  ❌ 学习能力: FAIL (Train AUC {final_train_auc:.3f} ≤ 0.55)")
    
    if is_improving:
        print(f"  ✅ 改进能力: PASS (提升 {train_auc_improvement:+.3f} > 0.01)")
        score += 25
    else:
        print(f"  ❌ 改进能力: FAIL (提升 {train_auc_improvement:+.3f} ≤ 0.01)")
    
    if is_stable:
        print(f"  ✅ 训练稳定: PASS (梯度范数 {avg_grad_norm:.3f} < 10.0)")
        score += 25
    else:
        print(f"  ❌ 训练稳定: FAIL (梯度范数 {avg_grad_norm:.3f} ≥ 10.0)")
    
    if is_generalizing:
        print(f"  ✅ 泛化能力: PASS (Val AUC {final_val_auc:.3f} > 0.52)")
        score += 25
    else:
        print(f"  ❌ 泛化能力: FAIL (Val AUC {final_val_auc:.3f} ≤ 0.52)")
    
    print()
    print(f"🏆 总体评分: {score}/100")
    
    if score >= 75:
        print("🎉 结论: 模型改进非常有价值，建议进行完整训练！")
        recommendation = "PROCEED"
    elif score >= 50:
        print("⚠️  结论: 模型改进有一定价值，可以尝试完整训练")
        recommendation = "CAUTIOUS"
    else:
        print("❌ 结论: 模型改进价值有限，建议重新审视架构")
        recommendation = "RECONSIDER"
    
    return recommendation, score

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset_path", type=str, default='./datasets/')
    parser.add_argument("--feature_path", type=str, default='./feature/')
    parser.add_argument("--seed", type=int, default=2024)
    parser.add_argument("--batch_size", type=int, default=16)
    parser.add_argument("--max_epochs", type=int, default=3)
    parser.add_argument("--sample_ratio", type=float, default=0.3, help="使用数据的比例进行快速验证")
    
    args = parser.parse_args()
    
    # Set random seed
    Seed_everything(seed=args.seed)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # Load training data
    train_df = pd.read_csv(args.dataset_path + 'PRO_Train335.csv')
    
    # Load protein data (sample for quick validation)
    protein_data = {}
    available_ids = []
    
    for pdb_id in tqdm(train_df['ID'].unique(), desc="Loading protein data"):
        try:
            pe_path = args.feature_path + f"{pdb_id}_pe_rw.tensor"
            if os.path.exists(pe_path):
                pe_tensor = torch.load(pe_path)
            else:
                temp_X = torch.load(args.feature_path + f"{pdb_id}_X.tensor")
                pe_tensor = torch.zeros(temp_X.shape[0], 32)

            protein_data[pdb_id] = (
                torch.load(args.feature_path + f"{pdb_id}_X.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_node_feature.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_mask.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_label.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_adj.tensor"),
                pe_tensor
            )
            available_ids.append(pdb_id)
        except FileNotFoundError:
            continue
    
    # Sample data for quick validation
    sample_size = int(len(available_ids) * args.sample_ratio)
    sampled_ids = np.random.choice(available_ids, sample_size, replace=False)
    
    # Split into train/val
    train_ids, val_ids = train_test_split(sampled_ids, test_size=0.3, random_state=args.seed)
    
    train_df_sample = train_df[train_df['ID'].isin(train_ids)].reset_index(drop=True)
    val_df_sample = train_df[train_df['ID'].isin(val_ids)].reset_index(drop=True)
    
    print(f"📊 快速验证数据: {len(train_ids)} 训练, {len(val_ids)} 验证")
    
    # Create datasets
    train_dataset = TaskDataset(train_df_sample, protein_data, 'label')
    val_dataset = TaskDataset(val_df_sample, protein_data, 'label')
    
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, 
                             collate_fn=train_dataset.collate_fn, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False,
                           collate_fn=val_dataset.collate_fn, num_workers=2)
    
    # Initialize model
    model = MVGNN(
        node_features=1024 + 14 + 20,
        edge_features=16,
        hidden_dim=128,
        num_encoder_layers=2,  # 减少层数加快验证
        k_neighbors=20,
        augment_eps=0.05,
        dropout=0.4
    ).to(device)
    
    # Use simple loss and optimizer for quick validation
    criterion = FocalLoss(alpha=0.25, gamma=2.0)
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-4, weight_decay=5e-5)
    
    print(f"🏗️  模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # Quick training test
    results = quick_train_test(model, train_loader, val_loader, criterion, optimizer, device, args.max_epochs)
    
    # Analyze results
    recommendation, score = analyze_results(results)
    
    # Final recommendation
    print("\n" + "="*50)
    if recommendation == "PROCEED":
        print("🚀 建议: 立即开始完整训练！")
        print("推荐命令:")
        print("python train.py --output_path ./output_gat_hybrid/ --loss_type combined --use_sam --sam_rho 0.015 --weight_decay 1e-4 --n_folds 5")
    elif recommendation == "CAUTIOUS":
        print("⚠️  建议: 可以尝试完整训练，但建议先用较保守的参数")
        print("推荐命令:")
        print("python train.py --output_path ./output_gat_hybrid_cautious/ --loss_type focal --use_sam --sam_rho 0.01 --weight_decay 5e-5 --n_folds 3")
    else:
        print("🔄 建议: 重新审视模型架构，可能需要进一步调试")
        print("建议检查:")
        print("1. GAT层的注意力机制是否正常工作")
        print("2. 混合图的融合策略是否合适")
        print("3. 数据预处理是否有问题")

if __name__ == '__main__':
    main()
