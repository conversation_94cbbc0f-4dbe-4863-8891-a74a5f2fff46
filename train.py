#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import pickle
import warnings
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import Data<PERSON>oader, RandomSampler
from sklearn.model_selection import <PERSON><PERSON><PERSON>
from tqdm import tqdm
import random
import argparse
from model import MVGNN
from utils import Seed_everything, Metric, Write_log, TaskDataset
from loss import FocalLoss, TverskyLoss, LovaszHinge, CombinedLoss, AdaptiveCombinedLoss
from noam_opt import get_std_opt

warnings.simplefilter('ignore')

def train_model(train_df, val_df, protein_data, model_class, config, fold, output_root='./output/', args=None):
    """
    Train a model for a single fold
    """
    # Setup paths and logging
    output_weight = output_root + "weight/"
    if not os.path.exists(output_weight):
        os.makedirs(output_weight)
    
    log = open(output_weight + f'fold{fold}.log', 'w', buffering=1)
    Write_log(log, str(config) + '\n')
    
    # Extract configuration parameters
    node_features = config['node_features']
    edge_features = config['edge_features']
    hidden_dim = config['hidden_dim']
    num_encoder_layers = config['num_encoder_layers']
    k_neighbors = config['k_neighbors']
    augment_eps = config['augment_eps']
    dropout = config['dropout']
    batch_size = config['batch_size']
    epochs = config['epochs']
    patience = config['patience']
    
    # Reset DataFrame indices to ensure continuous indexing
    train_df_reset = train_df.reset_index(drop=True)
    val_df_reset = val_df.reset_index(drop=True)

    # Create datasets and dataloaders
    train_dataset = TaskDataset(train_df_reset, protein_data, 'label')
    val_dataset = TaskDataset(val_df_reset, protein_data, 'label')

    # Use RandomSampler for training with replacement and sample_num
    train_sampler = RandomSampler(train_dataset, replacement=True, num_samples=config['num_samples'])

    train_dataloader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        sampler=train_sampler,
        collate_fn=train_dataset.collate_fn,
        num_workers=args.num_workers,
        prefetch_factor=2
    )
    
    val_dataloader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        collate_fn=val_dataset.collate_fn,
        shuffle=False,
        drop_last=False,
        num_workers=args.num_workers,
        prefetch_factor=2
    )
    
    # Initialize model
    model = model_class(
        node_features, 
        edge_features, 
        hidden_dim, 
        num_encoder_layers, 
        k_neighbors, 
        augment_eps, 
        dropout
    )
    model.cuda()
    
    # Use DataParallel if multiple GPUs are available
    if torch.cuda.device_count() > 1:
        print(f"Using {torch.cuda.device_count()} GPUs")
        model = nn.DataParallel(model)
    
    # Initialize loss function and optimizer
    loss_type = config.get('loss_type', 'focal')

    if loss_type == 'focal':
        print("Using Focal Loss")
        criterion = FocalLoss(alpha=config.get('focal_alpha', 0.25), gamma=config.get('focal_gamma', 2.0))

    elif loss_type == 'tversky':
        print("Using Tversky Loss")
        criterion = TverskyLoss(
            alpha=config.get('tversky_alpha', 0.3),
            beta=config.get('tversky_beta', 0.7)
        )

    elif loss_type == 'lovasz':
        print("Using Lovasz-Hinge Loss")
        criterion = LovaszHinge(per_sample=True)

    elif loss_type == 'combined':
        print("Using Combined Loss (Focal + Tversky + Lovasz)")
        criterion = CombinedLoss(
            focal_weight=config.get('focal_weight', 0.4),
            tversky_weight=config.get('tversky_weight', 0.4),
            lovasz_weight=config.get('lovasz_weight', 0.2),
            focal_alpha=config.get('focal_alpha', 0.25),
            focal_gamma=config.get('focal_gamma', 2.0),
            tversky_alpha=config.get('tversky_alpha', 0.3),
            tversky_beta=config.get('tversky_beta', 0.7)
        )

    elif loss_type == 'adaptive':
        print("Using Adaptive Combined Loss")
        criterion = AdaptiveCombinedLoss(
            focal_alpha=config.get('focal_alpha', 0.25),
            focal_gamma=config.get('focal_gamma', 2.0),
            tversky_alpha=config.get('tversky_alpha', 0.3),
            tversky_beta=config.get('tversky_beta', 0.7)
        )

    else:
        print("Using Focal Loss (default)")
        criterion = FocalLoss(alpha=config.get('focal_alpha', 0.25), gamma=config.get('focal_gamma', 2.0))

    use_sam = config.get('use_sam', False)
    sam_rho = config.get('sam_rho', 0.05)
    # 使用命令行参数中的weight_decay
    optimizer = get_std_opt(args.task, model.parameters(), hidden_dim, use_sam=use_sam, sam_rho=sam_rho, weight_decay=args.weight_decay)
    
    # Training loop
    best_val_auc = 0
    best_epoch = 0
    no_improvement = 0
    
    for epoch in range(epochs):
        # Update adaptive loss epoch if using adaptive loss
        if hasattr(criterion, 'set_epoch'):
            criterion.set_epoch(epoch)

        # Training phase
        model.train()
        train_loss = 0
        train_preds = []
        train_labels = []
        
        for data in tqdm(train_dataloader, desc=f"Epoch {epoch+1}/{epochs} - Training"):
            _, protein_X, protein_node_features, protein_masks, y, adj, pe = data

            # Move data to GPU
            protein_X = protein_X.cuda()
            protein_node_features = protein_node_features.cuda()
            protein_masks = protein_masks.cuda()
            y = y.cuda()
            adj = adj.cuda()
            pe = pe.cuda()

            # 定义closure函数用于SAM优化器
            def closure():
                optimizer.zero_grad()
                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)

                # Calculate loss on masked positions only
                loss = 0
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:  # Ensure there are valid positions
                        loss += criterion(outputs[i][mask], y[i][mask])

                loss = loss / len(protein_masks)  # Average loss across batch
                loss.backward()
                return loss

            # 检查是否使用SAM优化器
            if use_sam:
                # SAM的两步更新
                optimizer.zero_grad()

                # 第一步：计算梯度并扰动参数
                loss = closure()

                # --- 关键修复：加入梯度裁剪 ---
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

                optimizer.first_step(zero_grad=True)

                # 第二步：在扰动位置重新计算梯度并更新
                closure()

                # --- 关键修复：第二步也加入梯度裁剪 ---
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

                optimizer.second_step(zero_grad=True)

                # 获取最终的输出用于指标计算
                with torch.no_grad():
                    outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
            else:
                # 标准优化器更新
                optimizer.zero_grad()
                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)

                # Calculate loss on masked positions only
                loss = 0
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:  # Ensure there are valid positions
                        loss += criterion(outputs[i][mask], y[i][mask])

                loss = loss / len(protein_masks)  # Average loss across batch

                # Backward pass and optimization
                loss.backward()

                # --- 关键修复：加入梯度裁剪 ---
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

                optimizer.step()

            train_loss += loss.item()

            # Collect predictions and labels for metrics
            with torch.no_grad():
                probs = torch.sigmoid(outputs)
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        train_preds.append(probs[i][mask].cpu().numpy())
                        train_labels.append(y[i][mask].cpu().numpy())
        
        # Calculate training metrics
        train_preds = np.concatenate([p.flatten() for p in train_preds])
        train_labels = np.concatenate([l.flatten() for l in train_labels])
        train_metrics = Metric(train_preds, train_labels)
        
        # Validation phase
        model.eval()
        val_loss = 0
        val_preds = []
        val_labels = []
        
        with torch.no_grad():
            for data in tqdm(val_dataloader, desc=f"Epoch {epoch+1}/{epochs} - Validation"):
                _, protein_X, protein_node_features, protein_masks, y, adj, pe = data

                # Move data to GPU
                protein_X = protein_X.cuda()
                protein_node_features = protein_node_features.cuda()
                protein_masks = protein_masks.cuda()
                y = y.cuda()
                adj = adj.cuda()
                pe = pe.cuda()

                # Forward pass
                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
                
                # Calculate loss on masked positions only
                loss = 0
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        loss += criterion(outputs[i][mask], y[i][mask])
                
                loss = loss / len(protein_masks)
                val_loss += loss.item()
                
                # Collect predictions and labels
                probs = torch.sigmoid(outputs)
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        val_preds.append(probs[i][mask].cpu().numpy())
                        val_labels.append(y[i][mask].cpu().numpy())
        
        # Calculate validation metrics
        val_preds = np.concatenate([p.flatten() for p in val_preds])
        val_labels = np.concatenate([l.flatten() for l in val_labels])
        val_metrics = Metric(val_preds, val_labels)
        
        # Log metrics
        log_message = (f"Epoch {epoch+1}/{epochs} - "
                      f"Train Loss: {train_loss/len(train_dataloader):.6f}, "
                      f"Val Loss: {val_loss/len(val_dataloader):.6f}, "
                      f"Train AUC: {train_metrics[0]:.6f}, "
                      f"Val AUC: {val_metrics[0]:.6f}, "
                      f"Train AUPRC: {train_metrics[1]:.6f}, "
                      f"Val AUPRC: {val_metrics[1]:.6f}, "
                      f"Train MCC: {train_metrics[2]:.6f}, "
                      f"Val MCC: {val_metrics[2]:.6f}, "
                      f"Train F1: {train_metrics[6]:.6f}, "
                      f"Val F1: {val_metrics[6]:.6f}")
        
        Write_log(log, log_message)
        
        # Check for improvement
        current_val_auc = val_metrics[0]  # Using AUC as the primary metric
        if current_val_auc > best_val_auc:
            best_val_auc = current_val_auc
            best_epoch = epoch
            no_improvement = 0

            # Save the best model
            if isinstance(model, nn.DataParallel):
                torch.save(model.module.state_dict(), output_weight + f'fold{fold}.ckpt')
            else:
                torch.save(model.state_dict(), output_weight + f'fold{fold}.ckpt')

            Write_log(log, f"Model saved at epoch {epoch+1} with validation AUC: {best_val_auc:.6f}")
        else:
            no_improvement += 1
            
        # Early stopping
        if no_improvement >= patience:
            Write_log(log, f"Early stopping at epoch {epoch+1}. Best epoch: {best_epoch+1} with validation AUC: {best_val_auc:.6f}")
            break

    log.close()
    return best_val_auc

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset_path", type=str, default='./datasets/')
    parser.add_argument("--feature_path", type=str, default='./feature/')
    parser.add_argument("--output_path", type=str, default='./output/')
    parser.add_argument("--task", type=str, default='PRO')
    parser.add_argument("--num_workers", type=int, default=8)
    parser.add_argument("--seed", type=int, default=2024)
    parser.add_argument("--n_folds", type=int, default=5)
    parser.add_argument("--use_sam", action='store_true', help='Use SAM (Sharpness-Aware Minimization) optimizer')
    parser.add_argument("--sam_rho", type=float, default=0.03, help='SAM rho parameter (perturbation radius)')
    parser.add_argument("--pe_type", type=str, default='rw', choices=['rw', 'lap'], help='Type of positional encoding to use (rw: Random Walk, lap: Laplacian)')
    parser.add_argument("--loss_type", type=str, default='combined',
                        choices=['focal', 'tversky', 'lovasz', 'combined', 'adaptive'],
                        help="Loss function type")

    # 组合损失权重参数
    parser.add_argument("--focal_weight", type=float, default=0.4,
                        help="Weight for focal loss in combined loss")
    parser.add_argument("--tversky_weight", type=float, default=0.5,
                        help="Weight for tversky loss in combined loss")
    parser.add_argument("--lovasz_weight", type=float, default=0.1,
                        help="Weight for lovasz loss in combined loss")

    # Tversky损失参数
    parser.add_argument("--tversky_alpha", type=float, default=0.3,
                        help="Tversky alpha (FP penalty)")
    parser.add_argument("--tversky_beta", type=float, default=0.7,
                        help="Tversky beta (FN penalty)")

    # 正则化参数
    parser.add_argument("--weight_decay", type=float, default=5e-5,
                        help="Weight decay for optimizer (L2 regularization)")

    args = parser.parse_args()
    
    # Set random seed for reproducibility
    Seed_everything(seed=args.seed)
    
    # Load training data
    train_df = pd.read_csv(args.dataset_path + 'PRO_Train335.csv')

    # Load protein data
    protein_data = {}
    for pdb_id in tqdm(train_df['ID'].unique(), desc="Loading protein data"):
        try:
            # 根据PE类型选择相应的文件
            pe_suffix = '_pe_rw.tensor' if args.pe_type == 'rw' else '_pe_lap.tensor'
            pe_path = args.feature_path + f"{pdb_id}{pe_suffix}"

            if os.path.exists(pe_path):
                pe_tensor = torch.load(pe_path)
            else:
                # 如果PE文件不存在，创建零向量作为占位符
                # 先加载X来获取节点数量
                temp_X = torch.load(args.feature_path + f"{pdb_id}_X.tensor")
                pe_tensor = torch.zeros(temp_X.shape[0], 32)  # 假设PE维度为32
                print(f"Warning: {args.pe_type.upper()}PE file not found for {pdb_id}, using zero tensor")

            protein_data[pdb_id] = (
                torch.load(args.feature_path + f"{pdb_id}_X.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_node_feature.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_mask.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_label.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_adj.tensor"),
                pe_tensor  # 添加PE张量
            )
        except FileNotFoundError as e:
            print(f"Warning: Could not load data for {pdb_id}: {e}")
            continue
    
    # Model configuration
    nn_config = {
        'node_features': 1024 + 14 + 20,  # ProtTrans + DSSP + BLOSUM62
        'edge_features': 16,
        'hidden_dim': 128,
        'num_encoder_layers': 4,
        'k_neighbors': 30,
        'augment_eps': 0.1, #0.1
        'dropout': 0.4,  #0.3
        'id_name': 'ID',
        'obj_max': 1,
        'epochs': 40,
        'patience': 8,
        'batch_size': 32,
        'num_samples': 335 * 5,  # 5x oversampling
        'folds': args.n_folds,
        'seed': args.seed,
        'focal_alpha': 0.25,
        'focal_gamma': 2.0,
        'use_sam': args.use_sam,  # 启用SAM优化器
        'sam_rho': args.sam_rho,  # SAM扰动参数
        'pe_type': args.pe_type,  # PE类型
        'loss_type': args.loss_type,  # 损失函数类型
        'focal_weight': args.focal_weight,
        'tversky_weight': args.tversky_weight,
        'lovasz_weight': args.lovasz_weight,
        'tversky_alpha': args.tversky_alpha,
        'tversky_beta': args.tversky_beta,
        'remark': args.task + f' binding site prediction with BLOSUM62 + {args.pe_type.upper()}PE + SAM + {args.loss_type.upper()}Loss'
    }
    
    # Create output directories
    output_root = args.output_path
    if not os.path.exists(output_root):
        os.makedirs(output_root)
    if not os.path.exists(output_root + "weight/"):
        os.makedirs(output_root + "weight/")
    
    # Filter training data to only include proteins with loaded features
    available_ids = list(protein_data.keys())
    train_df = train_df[train_df['ID'].isin(available_ids)]
    print(f"Using {len(available_ids)} proteins with complete feature data out of {len(train_df['ID'].unique())} total proteins")

    # K-fold cross-validation
    kf = KFold(n_splits=args.n_folds, shuffle=True, random_state=args.seed)
    fold_metrics = []

    for fold, (train_idx, val_idx) in enumerate(kf.split(available_ids)):
        print(f"\n{'='*50}\nTraining Fold {fold+1}/{args.n_folds}\n{'='*50}")

        # Split data by protein IDs to avoid data leakage
        train_ids = [available_ids[i] for i in train_idx]
        val_ids = [available_ids[i] for i in val_idx]

        fold_train_df = train_df[train_df['ID'].isin(train_ids)]
        fold_val_df = train_df[train_df['ID'].isin(val_ids)]
        
        # Train the model for this fold
        fold_metric = train_model(
            fold_train_df, 
            fold_val_df, 
            protein_data, 
            MVGNN, 
            nn_config, 
            fold, 
            output_root, 
            args
        )
        
        fold_metrics.append(fold_metric)
    
    # Print overall results
    print(f"\n{'='*50}\nCross-Validation Results\n{'='*50}")
    for fold, metric in enumerate(fold_metrics):
        print(f"Fold {fold+1}: AUC = {metric:.6f}")
    print(f"Average AUC: {np.mean(fold_metrics):.6f} ± {np.std(fold_metrics):.6f}")

if __name__ == '__main__':
    main()
