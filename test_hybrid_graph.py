#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合图改进测试脚本
测试新的混合图架构是否正常工作，并与原始架构进行对比
"""

import torch
import torch.nn as nn
import numpy as np
from model import MVGNN
from self_attention import e_idx_to_adj
import time

def test_hybrid_graph_functionality():
    """测试混合图功能的基本正确性"""
    print("🧪 测试混合图功能...")
    
    # 设置测试参数
    batch_size = 2
    seq_len = 50
    k_neighbors = 10
    
    # 创建模拟数据
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 模拟 E_idx (k-NN邻居索引)
    E_idx = torch.randint(0, seq_len, (batch_size, seq_len, k_neighbors)).to(device)
    
    # 模拟原始邻接矩阵 adj
    adj = torch.rand(batch_size, seq_len, seq_len).to(device)
    adj = (adj > 0.7).float()  # 二值化
    
    # 测试 e_idx_to_adj 函数
    print("  ✓ 测试 E_idx 到邻接矩阵转换...")
    adj_knn = e_idx_to_adj(E_idx, seq_len)
    
    # 验证输出形状
    assert adj_knn.shape == (batch_size, seq_len, seq_len), f"形状错误: {adj_knn.shape}"
    
    # 验证对称性
    adj_knn_T = adj_knn.transpose(1, 2)
    assert torch.allclose(adj_knn, adj_knn_T, atol=1e-6), "邻接矩阵不对称"
    
    # 测试混合图创建
    print("  ✓ 测试混合图创建...")
    adj_hybrid = torch.max(adj_knn, adj)
    
    # 验证混合图包含了两种图的信息
    assert torch.all(adj_hybrid >= adj), "混合图应该包含原始邻接矩阵的所有边"
    assert torch.all(adj_hybrid >= adj_knn), "混合图应该包含k-NN图的所有边"
    
    print("  ✅ 混合图功能测试通过！")
    return True

def test_model_forward_pass():
    """测试模型前向传播"""
    print("🚀 测试模型前向传播...")
    
    # 模型参数
    node_features = 1058  # 根据你的实际特征维度
    edge_features = 128
    hidden_dim = 128
    num_encoder_layers = 2  # 减少层数以加快测试
    k_neighbors = 10
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = MVGNN(
        node_features=node_features,
        edge_features=edge_features, 
        hidden_dim=hidden_dim,
        num_encoder_layers=num_encoder_layers,
        k_neighbors=k_neighbors,
        dropout=0.1
    ).to(device)
    
    # 创建模拟输入数据
    batch_size = 2
    seq_len = 50
    
    X = torch.randn(batch_size, seq_len, 3).to(device)  # 3D坐标
    V = torch.randn(batch_size, seq_len, node_features).to(device)  # 节点特征
    mask = torch.ones(batch_size, seq_len).to(device)  # 掩码
    adj = torch.rand(batch_size, seq_len, seq_len).to(device)
    adj = (adj > 0.7).float()  # 二值化邻接矩阵
    pe = torch.randn(batch_size, seq_len, 32).to(device)  # PE特征
    
    # 前向传播
    print("  ✓ 执行前向传播...")
    start_time = time.time()
    
    with torch.no_grad():
        logits = model(X, V, mask, adj, pe)
    
    forward_time = time.time() - start_time
    
    # 验证输出
    expected_shape = (batch_size, seq_len)
    assert logits.shape == expected_shape, f"输出形状错误: {logits.shape}, 期望: {expected_shape}"
    
    print(f"  ✅ 前向传播成功！耗时: {forward_time:.3f}s")
    print(f"  📊 输出统计: min={logits.min():.3f}, max={logits.max():.3f}, mean={logits.mean():.3f}")
    
    return True

def compare_graph_connectivity():
    """比较不同图的连通性"""
    print("📈 分析图连通性...")
    
    batch_size = 1
    seq_len = 30
    k_neighbors = 8
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模拟数据
    E_idx = torch.randint(0, seq_len, (batch_size, seq_len, k_neighbors)).to(device)
    adj = torch.rand(batch_size, seq_len, seq_len).to(device)
    adj = (adj > 0.8).float()  # 稀疏的接触图
    
    # 转换和融合
    adj_knn = e_idx_to_adj(E_idx, seq_len)
    adj_hybrid = torch.max(adj_knn, adj)
    
    # 计算连通性统计
    def count_edges(adj_matrix):
        return adj_matrix.sum().item()
    
    def count_connected_components(adj_matrix):
        # 简化的连通分量计算（仅用于演示）
        adj_np = adj_matrix[0].cpu().numpy()
        return np.linalg.matrix_rank(adj_np)
    
    edges_original = count_edges(adj)
    edges_knn = count_edges(adj_knn)
    edges_hybrid = count_edges(adj_hybrid)
    
    print(f"  📊 边数统计:")
    print(f"    原始接触图: {edges_original}")
    print(f"    k-NN图: {edges_knn}")
    print(f"    混合图: {edges_hybrid}")
    print(f"    增加的边数: {edges_hybrid - edges_original}")
    print(f"    连通性提升: {(edges_hybrid / edges_original - 1) * 100:.1f}%")
    
    return True

def main():
    """主测试函数"""
    print("🔬 混合图架构改进测试")
    print("=" * 50)
    
    try:
        # 基础功能测试
        test_hybrid_graph_functionality()
        print()
        
        # 模型前向传播测试
        test_model_forward_pass()
        print()
        
        # 图连通性分析
        compare_graph_connectivity()
        print()
        
        print("🎉 所有测试通过！混合图架构已成功集成。")
        print("\n📋 下一步建议:")
        print("1. 在完整数据集上训练模型")
        print("2. 与原始架构进行性能对比")
        print("3. 进行消融实验验证混合图的贡献")
        print("4. 调优混合策略（当前使用max，可尝试加权融合）")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
