=== STEP 3: MOR<PERSON> ENCODER LAYERS ===
{'node_features': 1058, 'edge_features': 16, 'hidden_dim': 128, 'num_encoder_layers': 4, 'k_neighbors': 20, 'augment_eps': 0.05, 'dropout': 0.2, 'id_name': 'ID', 'obj_max': 1, 'epochs': 12, 'patience': 6, 'batch_size': 16, 'seed': 2024, 'use_sam': True, 'sam_rho': 0.01, 'remark': 'STEP 3: Hybrid graph with 4 encoder layers'}

STEP 3: Using single GPU with 4 encoder layers
STEP 3: Using CombinedLoss
STEP 3: Using NoamOpt + SAM with conservative parameters (rho=0.01)
