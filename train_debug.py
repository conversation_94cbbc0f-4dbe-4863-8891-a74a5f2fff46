#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试版本的训练脚本
使用简化的配置来诊断和修复训练问题
"""
import os
import pickle
import warnings
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import <PERSON><PERSON><PERSON><PERSON>, RandomSampler
from sklearn.model_selection import KFold
from tqdm import tqdm
import random
import argparse
from model import MVGNN
from utils import Seed_everything, Metric, Write_log, TaskDataset
from loss import FocalLoss

warnings.simplefilter('ignore')

def train_model_debug(train_df, val_df, protein_data, model_class, config, fold, output_root='./output_debug/', args=None):
    """
    调试版本的训练函数 - 使用简化配置
    """
    # Setup paths and logging
    output_weight = output_root + "weight/"
    if not os.path.exists(output_weight):
        os.makedirs(output_weight)
    
    log = open(output_weight + f'fold{fold}_debug.log', 'w', buffering=1)
    Write_log(log, "=== DEBUG MODE ===")
    Write_log(log, str(config) + '\n')
    
    # Extract configuration parameters
    node_features = config['node_features']
    edge_features = config['edge_features']
    hidden_dim = config['hidden_dim']
    num_encoder_layers = config['num_encoder_layers']
    k_neighbors = config['k_neighbors']
    augment_eps = config['augment_eps']
    dropout = config['dropout']
    batch_size = config['batch_size']
    epochs = config['epochs']
    patience = config['patience']
    
    # Reset DataFrame indices
    train_df_reset = train_df.reset_index(drop=True)
    val_df_reset = val_df.reset_index(drop=True)

    # Create datasets and dataloaders
    train_dataset = TaskDataset(train_df_reset, protein_data, 'label')
    val_dataset = TaskDataset(val_df_reset, protein_data, 'label')

    # 简化采样策略
    train_dataloader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,  # 简单的shuffle
        collate_fn=train_dataset.collate_fn,
        num_workers=2,  # 减少worker数量
        prefetch_factor=1
    )
    
    val_dataloader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        collate_fn=val_dataset.collate_fn,
        shuffle=False,
        drop_last=False,
        num_workers=2,
        prefetch_factor=1
    )
    
    # Initialize model
    model = model_class(
        node_features, 
        edge_features, 
        hidden_dim, 
        num_encoder_layers, 
        k_neighbors, 
        augment_eps, 
        dropout
    )
    model.cuda()
    
    # 简化：不使用DataParallel进行调试
    Write_log(log, "DEBUG MODE: Using single GPU")
    
    # 简化：只使用FocalLoss
    Write_log(log, "DEBUG MODE: Using simple Focal Loss")
    criterion = FocalLoss(alpha=0.25, gamma=2.0)
    
    # 关键修复：使用简单的Adam优化器
    Write_log(log, "DEBUG MODE: Using simple Adam optimizer with fixed LR")
    optimizer = torch.optim.Adam(
        model.parameters(), 
        lr=1e-4,  # 很小的固定学习率
        weight_decay=1e-5,
        eps=1e-8
    )
    
    # Training loop
    best_val_auc = 0
    best_epoch = 0
    no_improvement = 0
    
    for epoch in range(epochs):
        # Training phase
        model.train()
        train_loss = 0
        train_preds = []
        train_labels = []
        
        # 添加梯度监控
        total_grad_norm = 0
        num_batches = 0
        
        for data in tqdm(train_dataloader, desc=f"Epoch {epoch+1}/{epochs} - Training"):
            _, protein_X, protein_node_features, protein_masks, y, adj, pe = data

            # Move data to GPU
            protein_X = protein_X.cuda()
            protein_node_features = protein_node_features.cuda()
            protein_masks = protein_masks.cuda()
            y = y.cuda()
            adj = adj.cuda()
            pe = pe.cuda()

            # 简化的训练步骤
            optimizer.zero_grad()
            outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)

            # Calculate loss on masked positions only
            loss = 0
            for i in range(len(protein_masks)):
                mask = protein_masks[i].bool()
                if mask.sum() > 0:
                    loss += criterion(outputs[i][mask], y[i][mask])

            loss = loss / len(protein_masks)

            # Backward pass
            loss.backward()
            
            # 关键修复：梯度裁剪
            grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            total_grad_norm += grad_norm.item()
            num_batches += 1
            
            optimizer.step()

            train_loss += loss.item()

            # Collect predictions and labels for metrics
            with torch.no_grad():
                probs = torch.sigmoid(outputs)
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        train_preds.append(probs[i][mask].cpu().numpy())
                        train_labels.append(y[i][mask].cpu().numpy())
        
        # Calculate training metrics
        train_preds = np.concatenate([p.flatten() for p in train_preds])
        train_labels = np.concatenate([l.flatten() for l in train_labels])
        train_metrics = Metric(train_preds, train_labels)
        
        avg_grad_norm = total_grad_norm / num_batches if num_batches > 0 else 0
        
        # Validation phase
        model.eval()
        val_loss = 0
        val_preds = []
        val_labels = []
        
        with torch.no_grad():
            for data in tqdm(val_dataloader, desc=f"Epoch {epoch+1}/{epochs} - Validation"):
                _, protein_X, protein_node_features, protein_masks, y, adj, pe = data

                # Move data to GPU
                protein_X = protein_X.cuda()
                protein_node_features = protein_node_features.cuda()
                protein_masks = protein_masks.cuda()
                y = y.cuda()
                adj = adj.cuda()
                pe = pe.cuda()

                # Forward pass
                outputs = model(protein_X, protein_node_features, protein_masks, adj, pe)
                
                # Calculate loss
                loss = 0
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        loss += criterion(outputs[i][mask], y[i][mask])
                
                loss = loss / len(protein_masks)
                val_loss += loss.item()
                
                # Collect predictions and labels
                probs = torch.sigmoid(outputs)
                for i in range(len(protein_masks)):
                    mask = protein_masks[i].bool()
                    if mask.sum() > 0:
                        val_preds.append(probs[i][mask].cpu().numpy())
                        val_labels.append(y[i][mask].cpu().numpy())
        
        # Calculate validation metrics
        val_preds = np.concatenate([p.flatten() for p in val_preds])
        val_labels = np.concatenate([l.flatten() for l in val_labels])
        val_metrics = Metric(val_preds, val_labels)
        
        # Enhanced logging with gradient info
        log_message = (f"Epoch {epoch+1}/{epochs} - "
                      f"Train Loss: {train_loss/len(train_dataloader):.6f}, "
                      f"Val Loss: {val_loss/len(val_dataloader):.6f}, "
                      f"Train AUC: {train_metrics[0]:.6f}, "
                      f"Val AUC: {val_metrics[0]:.6f}, "
                      f"Train AUPRC: {train_metrics[1]:.6f}, "
                      f"Val AUPRC: {val_metrics[1]:.6f}, "
                      f"Grad Norm: {avg_grad_norm:.6f}")
        
        Write_log(log, log_message)
        
        # Check for improvement
        current_val_auc = val_metrics[0]
        if current_val_auc > best_val_auc:
            best_val_auc = current_val_auc
            best_epoch = epoch
            no_improvement = 0

            # Save the best model
            torch.save(model.state_dict(), output_weight + f'fold{fold}_debug.ckpt')
            Write_log(log, f"Model saved at epoch {epoch+1} with validation AUC: {best_val_auc:.6f}")
        else:
            no_improvement += 1
            
        # Early stopping
        if no_improvement >= patience:
            Write_log(log, f"Early stopping at epoch {epoch+1}. Best epoch: {best_epoch+1} with validation AUC: {best_val_auc:.6f}")
            break

    log.close()
    return best_val_auc

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset_path", type=str, default='./datasets/')
    parser.add_argument("--feature_path", type=str, default='./feature/')
    parser.add_argument("--output_path", type=str, default='./output_debug/')
    parser.add_argument("--task", type=str, default='PRO')
    parser.add_argument("--num_workers", type=int, default=2)
    parser.add_argument("--seed", type=int, default=2024)
    parser.add_argument("--n_folds", type=int, default=1)  # 只训练一个fold进行调试
    
    args = parser.parse_args()
    
    # Set random seed
    Seed_everything(seed=args.seed)
    
    # Load training data
    train_df = pd.read_csv(args.dataset_path + 'PRO_Train335.csv')

    # Load protein data
    protein_data = {}
    for pdb_id in tqdm(train_df['ID'].unique(), desc="Loading protein data"):
        try:
            pe_path = args.feature_path + f"{pdb_id}_pe_rw.tensor"
            if os.path.exists(pe_path):
                pe_tensor = torch.load(pe_path)
            else:
                temp_X = torch.load(args.feature_path + f"{pdb_id}_X.tensor")
                pe_tensor = torch.zeros(temp_X.shape[0], 32)
                print(f"Warning: PE file not found for {pdb_id}, using zero tensor")

            protein_data[pdb_id] = (
                torch.load(args.feature_path + f"{pdb_id}_X.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_node_feature.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_mask.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_label.tensor"),
                torch.load(args.feature_path + f"{pdb_id}_adj.tensor"),
                pe_tensor
            )
        except FileNotFoundError as e:
            print(f"Warning: Could not load data for {pdb_id}: {e}")
            continue
    
    # 简化的模型配置
    nn_config = {
        'node_features': 1024 + 14 + 20,
        'edge_features': 16,
        'hidden_dim': 128,
        'num_encoder_layers': 2,  # 减少层数
        'k_neighbors': 20,  # 减少邻居数
        'augment_eps': 0.05,  # 减少数据增强
        'dropout': 0.2,  # 减少dropout
        'id_name': 'ID',
        'obj_max': 1,
        'epochs': 15,  # 减少epoch数
        'patience': 5,
        'batch_size': 16,  # 减少batch size
        'seed': args.seed,
        'remark': 'DEBUG MODE: Simplified hybrid graph with gradient clipping and normalization'
    }
    
    # Create output directories
    output_root = args.output_path
    if not os.path.exists(output_root):
        os.makedirs(output_root)
    if not os.path.exists(output_root + "weight/"):
        os.makedirs(output_root + "weight/")
    
    # Filter training data
    available_ids = list(protein_data.keys())
    train_df = train_df[train_df['ID'].isin(available_ids)]
    print(f"Using {len(available_ids)} proteins for debugging")

    # 简化：只训练一个fold
    kf = KFold(n_splits=5, shuffle=True, random_state=args.seed)
    train_idx, val_idx = next(iter(kf.split(available_ids)))
    
    print(f"\n{'='*50}\nDEBUG Training\n{'='*50}")

    # Split data
    train_ids = [available_ids[i] for i in train_idx]
    val_ids = [available_ids[i] for i in val_idx]

    fold_train_df = train_df[train_df['ID'].isin(train_ids)]
    fold_val_df = train_df[train_df['ID'].isin(val_ids)]
    
    # Train the model
    fold_metric = train_model_debug(
        fold_train_df, 
        fold_val_df, 
        protein_data, 
        MVGNN, 
        nn_config, 
        0, 
        output_root, 
        args
    )
    
    print(f"\nDEBUG Training Result: AUC = {fold_metric:.6f}")

if __name__ == '__main__':
    main()
