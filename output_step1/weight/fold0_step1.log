=== STEP 1: COMBINED LOSS RESTORATION ===
{'node_features': 1058, 'edge_features': 16, 'hidden_dim': 128, 'num_encoder_layers': 2, 'k_neighbors': 20, 'augment_eps': 0.05, 'dropout': 0.2, 'id_name': 'ID', 'obj_max': 1, 'epochs': 10, 'patience': 5, 'batch_size': 16, 'seed': 2024, 'remark': 'STEP 1: Hybrid graph with CombinedLoss restoration'}

STEP 1: Using single GPU
STEP 1: Using CombinedLoss with conservative weights
STEP 1: Keeping simple Adam optimizer
