=== STEP 2: NOAM + SAM RESTORATION ===
{'node_features': 1058, 'edge_features': 16, 'hidden_dim': 128, 'num_encoder_layers': 2, 'k_neighbors': 20, 'augment_eps': 0.05, 'dropout': 0.2, 'id_name': 'ID', 'obj_max': 1, 'epochs': 10, 'patience': 5, 'batch_size': 16, 'seed': 2024, 'use_sam': True, 'sam_rho': 0.01, 'remark': 'STEP 2: Hybrid graph with NoamOpt + SAM (rho=0.01)'}

STEP 2: Using single GPU
STEP 2: Using CombinedLoss
STEP 2: Using NoamOpt + SAM with conservative parameters (rho=0.01)
