=== DEBUG MODE ===
{'node_features': 1058, 'edge_features': 16, 'hidden_dim': 128, 'num_encoder_layers': 2, 'k_neighbors': 20, 'augment_eps': 0.05, 'dropout': 0.2, 'id_name': 'ID', 'obj_max': 1, 'epochs': 15, 'patience': 5, 'batch_size': 16, 'seed': 2024, 'remark': 'DEBUG MODE: Simplified hybrid graph with gradient clipping and normalization'}

DEBUG MODE: Using single GPU
DEBUG MODE: Using simple Focal Loss
DEBUG MODE: Using simple Adam optimizer with fixed LR
